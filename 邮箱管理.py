import poplib
import email
import re
import time
import sys
import os
import queue
import threading
import multiprocessing
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
from bs4 import BeautifulSoup
import random
import string

from 配置管理 import get_config, save_config

# EMOJI常量定义
EMOJI = {
    "INFO": "ℹ️",
    "ERROR": "❌",
    "SUCCESS": "✅",
    "WARNING": "⚠️",
    "LOGIN": "🔑",
    "EMAIL": "📧",
    "USER": "👤",
    "COPY": "📋",
    "RESET": "🔄",
    "START": "▶️",
    "STOP": "⏹️"
}

# 使用绝对路径确保文件位置统一
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

# --- 全局配置 ---
POP3_SERVER = 'pop.2925.com'
POP3_PORT = 110
REFRESH_INTERVAL_SECONDS = 1
MAX_WAIT_SECONDS = 90

class EmailManager:
    """统一的邮箱管理类，整合所有邮箱相关功能"""
    
    def __init__(self):
        self.config = get_config()
        self.email_prefix = ""
        self.email_password = ""
        self.load_credentials()
    
    def load_credentials(self):
        """从配置文件加载邮箱凭据"""
        if self.config:
            self.email_prefix = self.config.get('Email', 'prefix', fallback='').strip()
            self.email_password = self.config.get('Email', 'password', fallback='').strip()
    
    def save_credentials(self, prefix, password):
        """保存邮箱凭据到配置文件"""
        if not self.config:
            return False
        
        self.config.set('Email', 'prefix', prefix)
        self.config.set('Email', 'password', password)
        
        if save_config(self.config):
            self.email_prefix = prefix
            self.email_password = password
            return True
        return False
    
    def generate_random_email(self):
        """生成随机邮箱地址"""
        if not self.email_prefix:
            return None
        
        random_part = ''.join(random.choices(string.ascii_lowercase, k=7))
        return f"{self.email_prefix}{random_part}@2925.com"
    
    def get_monitoring_email(self):
        """获取用于监控的固定邮箱地址"""
        if not self.email_prefix:
            return None
        return f"{self.email_prefix}@2925.com"
    
    @staticmethod
    def decode_payload(payload, charset):
        """安全地用给定字符集解码负载"""
        try:
            return payload.decode(charset)
        except (UnicodeDecodeError, LookupError):
            return payload.decode('gbk', errors='ignore')
    
    @staticmethod
    def get_clean_body_from_msg(msg):
        """解析邮件消息并返回纯净的文本正文"""
        body_content, html_content = "", ""
        if msg.is_multipart():
            for part in msg.walk():
                if part.get('Content-Disposition', '').startswith('attachment'):
                    continue
                payload = part.get_payload(decode=True)
                if not payload:
                    continue
                charset = part.get_content_charset() or 'utf-8'
                content_type = part.get_content_type()
                if content_type == 'text/plain':
                    body_content = EmailManager.decode_payload(payload, charset)
                elif content_type == 'text/html':
                    html_content = EmailManager.decode_payload(payload, charset)
        else:
            if not msg.get('Content-Disposition', '').startswith('attachment'):
                payload = msg.get_payload(decode=True)
                charset = msg.get_content_charset() or 'utf-8'
                content_type = msg.get_content_type()
                if content_type == 'text/plain':
                    body_content = EmailManager.decode_payload(payload, charset)
                elif content_type == 'text/html':
                    html_content = EmailManager.decode_payload(payload, charset)
        
        if not body_content.strip() and html_content:
            soup = BeautifulSoup(html_content, 'lxml')
            return soup.get_text(separator='\n', strip=True)
        return body_content
    
    @staticmethod
    def find_code_in_text(body_text):
        """使用正则表达式在字符串中查找6位验证码"""
        patterns = [r'\b\d{6}\b', r'\b\d{3}\s\d{3}\b', r'\b(?:\d\s){5}\d\b']
        for pattern in patterns:
            match = re.search(pattern, body_text)
            if match:
                return match.group(0).replace(" ", "")
        return None
    
    def establish_baseline(self, server, output_queue):
        """获取当前所有邮件的UIDL，建立基线"""
        try:
            resp, uid_lines, octets = server.uidl()
            seen_uids = {line.split()[1] for line in uid_lines}
            output_queue.put(f"基线已建立，当前有 {len(seen_uids)} 封邮件。开始监控新邮件...")
            return seen_uids
        except Exception as e:
            output_queue.put(f"建立基线时出错: {e}")
            return None
    
    def monitor_email(self, email_account, email_password, output_queue):
        """主执行逻辑：建立基线，然后持续监控新邮件"""
        output_queue.put(f"正在监控邮箱: {email_account}")
        
        # 首次连接并建立基线
        try:
            server = poplib.POP3(POP3_SERVER, port=POP3_PORT, timeout=20)
            server.user(email_account)
            server.pass_(email_password)
            output_queue.put("验证成功。")
            seen_uids = self.establish_baseline(server, output_queue)
            server.quit()
            if seen_uids is None:
                return
        except poplib.error_proto as e:
            output_queue.put(f"错误：登录失败。请检查凭据。 ({e})")
            return
        except Exception as e:
            output_queue.put(f"连接或建立基线时发生未知错误: {e}")
            return

        # 进入监控循环
        loop_counter = 0
        while True:
            try:
                # 检查停止标志
                try:
                    if output_queue.get_nowait() == 'STOP':
                        break
                except queue.Empty:
                    pass

                server = poplib.POP3(POP3_SERVER, port=POP3_PORT, timeout=20)
                server.user(email_account)
                server.pass_(email_password)
                
                resp, uid_lines, octets = server.uidl()
                
                # 创建当前邮件UID到消息号的映射
                current_uid_map = {
                    parts[1]: parts[0]
                    for line in uid_lines
                    if len(parts := line.split()) == 2
                }

                # 找出新的UID
                new_uids = set(current_uid_map.keys()) - seen_uids
                
                if new_uids:
                    loop_counter = 0
                    output_queue.put(f"\n发现 {len(new_uids)} 封新邮件，正在检查...")
                    # 将新邮件按消息号排序，确保先检查最新的
                    new_messages = sorted(
                        [(int(current_uid_map[uid]), uid) for uid in new_uids],
                        key=lambda x: x[0],
                        reverse=True
                    )

                    for msg_num, uid in new_messages:
                        resp, lines, octets = server.retr(msg_num)
                        msg_content = b'\r\n'.join(lines)
                        msg = email.message_from_bytes(msg_content)
                        
                        body = self.get_clean_body_from_msg(msg)
                        code = self.find_code_in_text(body)
                        
                        if code:
                            output_queue.put(f"成功提取到新邮件中的验证码: {code}")
                            output_queue.put(f"VERIFICATION_CODE:{code}")
                            server.quit()
                            return
                    
                    # 如果检查完所有新邮件都没找到验证码，则更新基线
                    output_queue.put("新邮件中未发现验证码，将继续监控...")
                    seen_uids.update(new_uids)
                else:
                    loop_counter += 1
                    # 每 15 秒打印一次状态，避免刷屏
                    if loop_counter % 15 == 1:
                        output_queue.put(f"没有新邮件，继续监控... ({time.strftime('%H:%M:%S')})")

                server.quit()
                time.sleep(REFRESH_INTERVAL_SECONDS)

            except KeyboardInterrupt:
                output_queue.put("\n程序已手动停止。")
                break
            except Exception as e:
                output_queue.put(f"\n监控循环中发生错误: {e}。等待10秒后重试...")
                time.sleep(10)
    
    def launch_email_client_process(self, email, password, status_callback, show_status=True, code_container=None):
        """使用 multiprocessing 在后台启动邮件客户端脚本"""
        try:
            if show_status:
                status_callback("正在验证邮箱凭据...")

            # 首先验证邮箱凭据
            if not self.verify_email_credentials(email, password, status_callback):
                if show_status:
                    status_callback("❌ 验证失败，停止启动邮箱客户端")
                return False

            if show_status:
                status_callback("✅ 验证成功，开始启动邮箱客户端进程")

            output_queue = multiprocessing.Queue()

            # 创建并启动子进程
            process = multiprocessing.Process(
                target=self.monitor_email,
                args=(email, password, output_queue)
            )
            process.daemon = True
            process.start()

            def log_output():
                """从队列中读取并处理子进程的输出"""
                while process.is_alive() or not output_queue.empty():
                    try:
                        line = output_queue.get(timeout=0.1)
                        stripped_line = line.strip()
                        # IPC logic to capture the code
                        if stripped_line.startswith("VERIFICATION_CODE:"):
                            try:
                                code = stripped_line.split(":", 1)[1]
                                if code_container is not None:
                                    code_container.append(code)
                            except IndexError:
                                pass

                        # Logging logic
                        status_callback(f"[邮箱客户端]: {stripped_line}")
                    except queue.Empty:
                        continue

                # 确保进程结束后清空队列
                while not output_queue.empty():
                    try:
                        line = output_queue.get_nowait()
                        status_callback(f"[邮箱客户端]: {line.strip()}")
                    except queue.Empty:
                        break

            threading.Thread(target=log_output, daemon=True).start()

            if show_status:
                status_callback(f"✅ 邮箱验证成功，监控已启动: {email}")
            return True

        except Exception as e:
            if show_status:
                status_callback(f"❌ 启动邮箱客户端时发生错误: {e}")
            return False

    def verify_email_credentials(self, email, password, status_callback):
        """验证邮箱凭据是否正确"""
        try:
            status_callback("正在连接邮箱服务器...")
            server = poplib.POP3(POP3_SERVER, port=POP3_PORT, timeout=10)

            status_callback("正在验证用户名...")
            server.user(email)

            status_callback("正在验证密码...")
            server.pass_(password)

            status_callback("✅ 邮箱凭据验证成功")
            server.quit()
            return True

        except poplib.error_proto as e:
            error_msg = str(e).lower()
            if 'user' in error_msg or 'password' in error_msg or 'auth' in error_msg:
                status_callback(f"❌ 邮箱凭据验证失败: 用户名或密码错误")
            else:
                status_callback(f"❌ 邮箱服务器错误: {e}")
            return False

        except Exception as e:
            status_callback(f"❌ 连接邮箱服务器失败: {e}")
            return False
    
    def wait_for_verification_code(self, status_callback, code_container):
        """轮询一个共享列表以获取验证码"""
        status_callback("正在等待接收验证码 (最长90秒)...")
        for i in range(MAX_WAIT_SECONDS):
            if code_container:
                code = code_container.pop(0)
                status_callback(f"COPY_AND_SHOW:{code}")
                return code
            
            # 每 15 秒更新一次状态
            if (i > 0 and (i + 1) % 15 == 0):
                status_callback(f"已等待 {i+1} 秒，接收中...")
                
            time.sleep(1)
            
        status_callback("等待验证码超时。")
        return None


class EmailClientWindow:
    """邮箱客户端窗口类"""

    def __init__(self, parent=None):
        self.parent = parent
        self.window = None
        self.email_manager = EmailManager()
        self.monitoring_process = None
        self.monitoring_thread = None

    def show(self):
        """显示邮箱客户端窗口"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            self.window.focus_force()
            return

        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("📧 邮箱验证码监控器")
        self.window.geometry("650x550")
        self.window.resizable(True, True)
        self.window.minsize(600, 500)

        # 设置窗口背景色
        self.window.configure(bg='#f8fafc')

        # 居中显示窗口
        if self.parent:
            self.window.transient(self.parent)
            self.window.grab_set()

        # 居中窗口
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

        # 创建界面
        self.create_widgets()

        # 设置窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.grid(row=0, column=0, sticky="nsew")
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)

        # 美化的标题区域
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, sticky="ew", pady=(0, 20))

        # 主标题
        title_label = ttk.Label(title_frame, text=f"{EMOJI['EMAIL']} 邮箱验证码监控器",
                               font=("Microsoft YaHei UI", 16, "bold"))
        title_label.pack()

        # 副标题
        subtitle_label = ttk.Label(title_frame, text="实时监控邮箱，自动获取验证码",
                                  font=("Microsoft YaHei UI", 10), foreground="#6b7280")
        subtitle_label.pack(pady=(5, 0))

        # 美化的登录信息框架
        login_frame = ttk.LabelFrame(main_frame, text="📋 登录信息", padding="15")
        login_frame.grid(row=1, column=0, sticky="ew", pady=(0, 15))
        login_frame.columnconfigure(1, weight=1)

        # 邮箱账号
        ttk.Label(login_frame, text="监控邮箱:", font=("Microsoft YaHei UI", 9, "bold")).grid(row=0, column=0, sticky="w", pady=(0, 10))
        self.email_entry = ttk.Entry(login_frame, state='readonly', font=("Consolas", 10), width=35)
        self.email_entry.grid(row=0, column=1, sticky="ew", padx=(10, 0))

        # 邮箱密码
        ttk.Label(login_frame, text="邮箱密码:").grid(row=1, column=0, sticky="w", pady=2)
        self.password_entry = ttk.Entry(login_frame, width=40, font=("Consolas", 10))
        self.password_entry.grid(row=1, column=1, sticky="ew", padx=(10, 0))
        self.password_entry.insert(0, self.email_manager.email_password)

        login_frame.columnconfigure(1, weight=1)

        # 控制按钮框架
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, sticky="ew", pady=(0, 10))

        self.start_button = ttk.Button(control_frame, text=f"{EMOJI['START']} 开始监控",
                                      command=self.start_monitoring)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_button = ttk.Button(control_frame, text=f"{EMOJI['STOP']} 停止监控",
                                     command=self.stop_monitoring, state='disabled')
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        self.clear_button = ttk.Button(control_frame, text="清空日志",
                                      command=self.clear_log)
        self.clear_button.pack(side=tk.LEFT)

        # 状态指示器
        self.status_label = ttk.Label(control_frame, text="● 就绪", foreground="green")
        self.status_label.pack(side=tk.RIGHT)

        # 监控日志
        log_frame = ttk.LabelFrame(main_frame, text="监控日志", padding="5")
        log_frame.grid(row=3, column=0, sticky="nsew", pady=(0, 10))

        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD,
                                                 state='disabled', font=("Consolas", 9))
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 验证码结果框架
        result_frame = ttk.LabelFrame(main_frame, text="验证码结果", padding="10")
        result_frame.grid(row=4, column=0, sticky="ew")

        ttk.Label(result_frame, text="验证码:").grid(row=0, column=0, sticky="w")
        self.code_entry = ttk.Entry(result_frame, width=20, font=("Consolas", 12, "bold"))
        self.code_entry.grid(row=0, column=1, sticky="ew", padx=(10, 10))

        self.copy_button = ttk.Button(result_frame, text="复制", command=self.copy_code)
        self.copy_button.grid(row=0, column=2)

        result_frame.columnconfigure(1, weight=1)

        # 设置权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)

        # 更新邮箱显示
        self.update_email_display()

    def update_email_display(self):
        """更新邮箱显示"""
        monitoring_email = self.email_manager.get_monitoring_email()
        if monitoring_email:
            self.email_entry.config(state='normal')
            self.email_entry.delete(0, tk.END)
            self.email_entry.insert(0, monitoring_email)
            self.email_entry.config(state='readonly')
        else:
            self.update_status("请先在主界面设置邮箱凭据")

    def start_monitoring(self):
        """开始监控邮箱"""
        if not self.email_manager.email_prefix or not self.password_entry.get():
            messagebox.showerror("错误", "请先设置邮箱凭据")
            return

        # 清空之前的验证码
        self.code_entry.delete(0, tk.END)

        # 在主线程中获取必要的数据
        email_to_watch = self.email_manager.get_monitoring_email()
        password = self.password_entry.get()

        def monitoring_task():
            # 标记验证是否成功
            verification_success = False

            def process_output(message):
                """处理监控输出"""
                nonlocal verification_success

                if "VERIFICATION_CODE:" in message:
                    try:
                        code = message.split("VERIFICATION_CODE:", 1)[1].strip()
                        self.window.after(0, self.on_code_received, code)
                    except IndexError:
                        self.window.after(0, self.update_status, message)
                elif "✅ 邮箱凭据验证成功" in message or "✅ 邮箱验证成功" in message:
                    verification_success = True
                    self.window.after(0, lambda: self.status_label.config(text="● 监控中", foreground="orange"))
                elif "❌" in message and ("验证失败" in message or "凭据" in message or "密码错误" in message):
                    # 验证失败，停止监控
                    self.window.after(0, self.stop_monitoring)
                    self.window.after(0, lambda: messagebox.showerror("验证失败", "邮箱凭据验证失败，请检查用户名和密码"))

                self.window.after(0, self.update_status, message)

            try:
                # 先设置为验证中状态
                self.window.after(0, lambda: self.status_label.config(text="● 验证中", foreground="blue"))

                # 启动邮箱客户端进程（现在包含验证逻辑）
                success = self.email_manager.launch_email_client_process(email_to_watch, password, process_output)

                if not success:
                    # 如果启动失败，恢复按钮状态
                    self.window.after(0, self.stop_monitoring)

            except Exception as e:
                self.window.after(0, self.update_status, f"监控出错: {e}")
                self.window.after(0, self.stop_monitoring)

        # 禁用开始按钮，启用停止按钮
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')

        # 启动监控线程
        self.monitoring_thread = threading.Thread(target=monitoring_task, daemon=True)
        self.monitoring_thread.start()

        self.update_status("正在验证邮箱凭据...")

    def stop_monitoring(self):
        """停止监控"""
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.status_label.config(text="● 已停止", foreground="red")
        self.update_status("监控已停止")

    def on_code_received(self, code):
        """收到验证码时的处理"""
        self.code_entry.delete(0, tk.END)
        self.code_entry.insert(0, code)
        self.status_label.config(text="● 收到验证码", foreground="blue")
        self.update_status(f"成功获取验证码: {code}")

        # 自动复制到剪贴板
        try:
            self.window.clipboard_clear()
            self.window.clipboard_append(code)
            self.update_status("验证码已自动复制到剪贴板")
        except:
            pass

        # 自动停止监控
        self.stop_monitoring()

    def copy_code(self):
        """复制验证码"""
        code = self.code_entry.get()
        if code:
            try:
                self.window.clipboard_clear()
                self.window.clipboard_append(code)
                self.update_status("验证码已复制到剪贴板")
            except Exception as e:
                self.update_status(f"复制失败: {e}")
        else:
            messagebox.showwarning("警告", "没有验证码可复制")

    def clear_log(self):
        """清空日志"""
        self.log_text.config(state='normal')
        self.log_text.delete('1.0', tk.END)
        self.log_text.config(state='disabled')

    def update_status(self, message):
        """更新状态日志"""
        if not self.window or not self.window.winfo_exists():
            return

        self.log_text.config(state='normal')
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.config(state='disabled')
        self.log_text.see(tk.END)

    def on_closing(self):
        """窗口关闭事件"""
        if self.stop_button['state'] == 'normal':
            self.stop_monitoring()
        self.window.destroy()


# 全局邮箱管理器实例
email_manager = EmailManager()

def show_email_client(parent=None):
    """显示邮箱客户端窗口"""
    client = EmailClientWindow(parent)
    client.show()
    return client

def generate_random_email(status_callback=None):
    """生成随机邮箱（兼容原有接口）"""
    email = email_manager.generate_random_email()
    if not email and status_callback:
        status_callback(f"{EMOJI['ERROR']} 邮箱前缀未设置，无法生成邮箱。请在GUI中设置。")
    return email

def launch_email_client_process(email, password, status_callback, show_status=True, code_container=None):
    """启动邮箱客户端进程（兼容原有接口）"""
    return email_manager.launch_email_client_process(email, password, status_callback, show_status, code_container)

def wait_for_verification_code(status_callback, code_container):
    """等待验证码（兼容原有接口）"""
    return email_manager.wait_for_verification_code(status_callback, code_container)

# === 网页操作功能 ===
def get_random_wait_time_web(config, timing_type='page_load_wait') -> float:
    """从配置中获取一个随机的等待时间，以模仿人类操作"""
    return random.uniform(0.5, 1.5)

def run_auto_login_flow(monitoring_email, login_email, password, status_callback):
    """执行完整的自动登录流程"""
    chrome_window_to_close = None
    try:
        if not password:
            status_callback(f"错误: 邮箱密码未设置，请在凭据中设置并保存。")
            return

        if not monitoring_email or "@" not in monitoring_email:
            status_callback(f"错误: 用于监控的邮箱无效。")
            return

        status_callback("正在启动邮箱监控...")
        code_container = [] # 创建共享列表
        if not launch_email_client_process(monitoring_email, password, status_callback, show_status=False, code_container=code_container):
            status_callback(f"错误: 无法启动邮箱客户端，自动登录中止。")
            return
        status_callback(f"邮箱监控已在后台启动，监控账号: {monitoring_email}")

        status_callback("正在获取当前Chrome浏览器URL...")
        # 导入Chrome URL获取函数
        import pywinauto
        from pywinauto.application import Application

        def get_current_chrome_url():
            """获取当前Chrome浏览器的URL"""
            try:
                app = Application(backend="uia").connect(title_re=".*- Google Chrome", class_name="Chrome_WidgetWin_1", timeout=10)
                dlg = app.top_window()
                wrapper = dlg.child_window(auto_id="view_1000", control_type="ToolBar")
                address_bar = wrapper.child_window(control_type="Edit")

                if address_bar.exists():
                    url = address_bar.get_value()
                    return url, dlg # Return both URL and the window handle
                else:
                    return None, None

            except pywinauto.findwindows.ElementNotFoundError:
                print("Error: Google Chrome window not found.")
                return None, None
            except Exception as e:
                print(f"An unknown error occurred: {e}")
                return None, None

        target_url, chrome_window_to_close = get_current_chrome_url()
        if not target_url:
            status_callback(f"错误: 获取URL失败，请确保Chrome在前台打开了一个页面。")
            return

        status_callback("正在打开新的浏览器窗口...")
        from DrissionPage import ChromiumOptions, ChromiumPage
        co = ChromiumOptions()

        # 从配置中读取浏览器路径并设置
        from 配置管理 import get_config
        config = get_config()
        if config:
            chrome_path = config.get('Browser', 'chrome_path', fallback=None)
            if chrome_path and os.path.exists(chrome_path):
                co.set_paths(browser_path=chrome_path)
                status_callback(f"使用配置文件中的Chrome路径: {chrome_path}")
            else:
                status_callback("配置文件中未找到有效Chrome路径，使用默认路径。")

        co.set_argument('--incognito')
        page = ChromiumPage(addr_or_opts=co)

        if chrome_window_to_close:
            status_callback("正在关闭原始Chrome窗口...")
            chrome_window_to_close.close()
            status_callback("原始窗口已关闭。")

        if not target_url.startswith('http'):
            target_url = 'https://' + target_url
        status_callback(f"成功获取URL: {target_url}")

        status_callback(f"使用随机邮箱进行登录: {login_email}")

        automate_login(page, target_url, login_email, status_callback, code_container)

    except Exception as e:
        status_callback(f"错误: 一键登录过程中发生未知错误: {e}")
        import traceback
        status_callback(traceback.format_exc())

def automate_login(page, url, email, status_callback, code_container):
    """使用 DrissionPage 的核心自动化任务"""
    try:
        status_callback(f"正在加载URL: {url}")
        page.get(url)

        # 步骤 1: 填写邮箱并点击 "Continue"
        status_callback("步骤 1: 正在填写邮箱并点击 'Continue'...")
        email_input = page.ele('@@name=email', timeout=10)
        email_input.input(email)
        continue_button = page.ele('@@type=submit', timeout=10)
        continue_button.click()
        status_callback("已点击 'Continue'，等待跳转...")

        # 步骤 2: 在下一页点击 "Email sign-in code"
        status_callback("步骤 2: 等待密码页面加载，准备点击验证码登录...")
        sign_in_code_button = page.ele('text:Email sign-in code', timeout=15)
        sign_in_code_button.click()
        status_callback("已点击 'Email sign-in code' 按钮。")

        # 步骤 3: 等待验证码
        status_callback("步骤 3: 启动验证码监听程序...")
        code = wait_for_verification_code(status_callback, code_container)

        if not code:
            final_message = "\n错误: 等待验证码超时。请手动输入验证码完成登录。\n\n注意：操作完成后，请您手动关闭此浏览器窗口。"
            status_callback(final_message)
            return

        # 步骤 4: 自动填写验证码
        try:
            status_callback(f"步骤 4: 成功获取验证码，正在自动填写...")
            for i, digit in enumerate(code):
                input_box = page.ele(f"@data-index={i}")
                input_box.input(digit)
                time.sleep(random.uniform(0.1, 0.3))

            status_callback("✅ 验证码填写完成。")
            final_message = "\n✅ 成功: 验证码已自动填写。\n\n如果页面没有自动跳转，请手动完成最后步骤。完成后请手动关闭此浏览器窗口。"
            status_callback(final_message)

        except Exception as e:
            status_callback(f"❌ 错误: 自动填写验证码时出错: {e}")
            final_message = "自动化流程中止，请手动输入验证码。"
            status_callback(final_message)

        return

    except Exception as e:
        error_msg = f"错误: 自动化过程中发生错误: {e}"
        status_callback(error_msg)
        import traceback
        status_callback(traceback.format_exc())

if __name__ == "__main__":
    # 独立运行测试
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    client = show_email_client()
    root.mainloop()
