import os
import sys
import configparser
from colorama import Fore, Style
from 工具模块 import get_user_documents_path, get_linux_cursor_path, get_default_driver_path, get_default_browser_path, EMOJI
import shutil
import datetime

# global config cache
_config_cache = None

def safe_log(message, level='info'):
    """安全的日志记录函数，用于GUI应用 - 现在不记录到文件"""
    # 不再记录日志到文件，静默处理
    pass

def setup_config():
    """Setup configuration file and return config object"""
    try:
        # get documents path
        docs_path = get_user_documents_path()
        if not docs_path or not os.path.exists(docs_path):
            # if documents path not found, use current directory
            safe_log("未找到文档路径，使用当前目录", 'warning')
            docs_path = os.path.abspath('.')
        
        # normalize path
        config_dir = os.path.normpath(os.path.join(docs_path, ".cursor-free-vip"))
        config_file = os.path.normpath(os.path.join(config_dir, "config.ini"))
        
        # create config directory, only print message when directory not exists
        dir_exists = os.path.exists(config_dir)
        try:
            os.makedirs(config_dir, exist_ok=True)
            if not dir_exists:  # only log message when directory not exists
                safe_log(f"配置目录已创建: {config_dir}")
        except Exception as e:
            # if cannot create directory, use temporary directory
            import tempfile
            temp_dir = os.path.normpath(os.path.join(tempfile.gettempdir(), ".cursor-free-vip"))
            temp_exists = os.path.exists(temp_dir)
            config_dir = temp_dir
            config_file = os.path.normpath(os.path.join(config_dir, "config.ini"))
            os.makedirs(config_dir, exist_ok=True)
            if not temp_exists:  # only log message when temporary directory not exists
                safe_log(f"因错误使用临时目录: {config_dir} (错误: {str(e)})", 'warning')
        
        # create config object
        config = configparser.ConfigParser()
        
        # Dynamically construct the user-specific Chrome path
        user_chrome_path = ''
        if sys.platform == "win32":
            local_app_data = os.getenv('LOCALAPPDATA')
            if local_app_data:
                user_chrome_path = os.path.join(local_app_data, 'Google', 'Chrome', 'Application', 'chrome.exe')

        # Default configuration
        default_config = {
            'Browser': {
                'default_browser': 'chrome',
                'chrome_path': user_chrome_path if user_chrome_path and os.path.exists(user_chrome_path) else get_default_browser_path('chrome'),
                'chrome_driver_path': get_default_driver_path('chrome'),
            },
            'Turnstile': {
                'handle_turnstile_time': '2',
                'handle_turnstile_random_time': '1-3'
            },
            'Timing': {
                'min_random_time': '0.1',
                'max_random_time': '0.8',
                'page_load_wait': '0.1-0.8',
                'input_wait': '0.3-0.8',
                'submit_wait': '0.5-1.5',
                'verification_code_input': '0.1-0.3',
                'verification_success_wait': '2-3',
                'verification_retry_wait': '2-3',
                'email_check_initial_wait': '4-6',
                'email_refresh_wait': '2-4',
                'settings_page_load_wait': '1-2',
                'failed_retry_time': '0.5-1',
                'retry_interval': '8-12',
                'max_timeout': '160'
            },
            'Utils': {
                'enabled_update_check': 'True',
                'enabled_force_update': 'False',
                'enabled_account_info': 'True'
            },
            'OAuth': {
                'show_selection_alert': False,  # 默认不显示选择提示弹窗
                'timeout': 120,
                'max_attempts': 3
            },
            'Token': {
                'refresh_server': 'https://token.cursorpro.com.cn',
                'enable_refresh': True
            },
            'Email': {
                'prefix': '',
                'password': ''
            }
        }

        # Add system-specific path configuration
        if sys.platform == "win32":
            appdata = os.getenv("APPDATA") or ''
            localappdata = os.getenv("LOCALAPPDATA") or ''

            if not appdata or not localappdata:
                print(f"{Fore.RED}{EMOJI['ERROR']} APPDATA or LOCALAPPDATA environment variable not found.{Style.RESET_ALL}")
                # Fallback to a temp directory to avoid crashing
                import tempfile
                appdata = localappdata = tempfile.gettempdir()

            default_config['WindowsPaths'] = {
                'storage_path': os.path.join(appdata, "Cursor", "User", "globalStorage", "storage.json"),
                'sqlite_path': os.path.join(appdata, "Cursor", "User", "globalStorage", "state.vscdb"),
                'machine_id_path': os.path.join(appdata, "Cursor", "machineId"),
                'cursor_path': os.path.join(localappdata, "Programs", "Cursor", "resources", "app"),
                'updater_path': os.path.join(localappdata, "cursor-updater"),
                'update_yml_path': os.path.join(localappdata, "Programs", "Cursor", "resources", "app-update.yml"),
                'product_json_path': os.path.join(localappdata, "Programs", "Cursor", "resources", "app", "product.json")
            }
            # Create storage directory
            os.makedirs(os.path.dirname(default_config['WindowsPaths']['storage_path']), exist_ok=True)
            
        elif sys.platform == "darwin":
            default_config['MacPaths'] = {
                'storage_path': os.path.abspath(os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/storage.json")),
                'sqlite_path': os.path.abspath(os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/state.vscdb")),
                'machine_id_path': os.path.expanduser("~/Library/Application Support/Cursor/machineId"),
                'cursor_path': "/Applications/Cursor.app/Contents/Resources/app",
                'updater_path': os.path.expanduser("~/Library/Application Support/cursor-updater"),
                'update_yml_path': "/Applications/Cursor.app/Contents/Resources/app-update.yml",
                'product_json_path': "/Applications/Cursor.app/Contents/Resources/app/product.json"
            }
            # Create storage directory
            os.makedirs(os.path.dirname(default_config['MacPaths']['storage_path']), exist_ok=True)
            
        elif sys.platform == "linux":
            # Get the actual user's home directory, handling both sudo and normal cases
            sudo_user = os.environ.get('SUDO_USER')
            current_user = sudo_user if sudo_user else (os.getenv('USER') or os.getenv('USERNAME'))
            
            if not current_user:
                current_user = os.path.expanduser('~').split('/')[-1]
            
            # Handle sudo case
            if sudo_user:
                actual_home = f"/home/<USER>"
                root_home = "/root"
            else:
                actual_home = f"/home/<USER>"
                root_home = None
            
            if not os.path.exists(actual_home):
                actual_home = os.path.expanduser("~")
            
            # Define base config directory
            config_base = os.path.join(actual_home, ".config")
            
            # Try both "Cursor" and "cursor" directory names in both user and root locations
            cursor_dir = None
            possible_paths = [
                os.path.join(config_base, "Cursor"),
                os.path.join(config_base, "cursor"),
                os.path.join(root_home, ".config", "Cursor") if root_home else None,
                os.path.join(root_home, ".config", "cursor") if root_home else None
            ]
            
            for path in possible_paths:
                if path and os.path.exists(path):
                    cursor_dir = path
                    break
            
            if not cursor_dir:
                print(f"{Fore.YELLOW}{EMOJI['WARNING']} 在 {config_base} 中未找到 Cursor 或 cursor 目录{Style.RESET_ALL}")
                if root_home:
                    print(f"{Fore.YELLOW}{EMOJI['INFO']} 同时检查了 {root_home}/.config{Style.RESET_ALL}")
                print(f"{Fore.YELLOW}{EMOJI['INFO']} 请确保 Cursor 已安装并至少运行过一次{Style.RESET_ALL}")
            
            # Define Linux paths using the found cursor directory
            storage_path = os.path.abspath(os.path.join(cursor_dir, "User/globalStorage/storage.json")) if cursor_dir else ""
            storage_dir = os.path.dirname(storage_path) if storage_path else ""
            
            # Verify paths and permissions
            try:
                # Check storage directory
                if storage_dir and not os.path.exists(storage_dir):
                    print(f"{Fore.YELLOW}{EMOJI['WARNING']} 未找到存储目录: {storage_dir}{Style.RESET_ALL}")
                    print(f"{Fore.YELLOW}{EMOJI['INFO']} 请确保 Cursor 已安装并至少运行过一次{Style.RESET_ALL}")
                
                # Check storage.json with more detailed verification
                if storage_path and os.path.exists(storage_path):
                    # Get file stats
                    try:
                        stat = os.stat(storage_path)
                        print(f"{Fore.GREEN}{EMOJI['INFO']} 找到存储文件: {storage_path}{Style.RESET_ALL}")
                        print(f"{Fore.GREEN}{EMOJI['INFO']} 文件大小: {stat.st_size} 字节{Style.RESET_ALL}")
                        print(f"{Fore.GREEN}{EMOJI['INFO']} 文件权限: {oct(stat.st_mode & 0o777)}{Style.RESET_ALL}")
                        print(f"{Fore.GREEN}{EMOJI['INFO']} 文件所有者: {stat.st_uid}{Style.RESET_ALL}")
                        print(f"{Fore.GREEN}{EMOJI['INFO']} 文件组: {stat.st_gid}{Style.RESET_ALL}")
                    except Exception as e:
                        print(f"{Fore.RED}{EMOJI['ERROR']} 获取文件状态时出错: {str(e)}{Style.RESET_ALL}")
                    
                    # Check if file is readable and writable
                    if not os.access(storage_path, os.R_OK | os.W_OK):
                        print(f"{Fore.RED}{EMOJI['ERROR']} 权限被拒绝: {storage_path}{Style.RESET_ALL}")
                        if sudo_user:
                            print(f"{Fore.YELLOW}{EMOJI['INFO']} 尝试运行: chown {sudo_user}:{sudo_user} {storage_path}{Style.RESET_ALL}")
                            print(f"{Fore.YELLOW}{EMOJI['INFO']} 和: chmod 644 {storage_path}{Style.RESET_ALL}")
                        else:
                            print(f"{Fore.YELLOW}{EMOJI['INFO']} 尝试运行: chown {current_user}:{current_user} {storage_path}{Style.RESET_ALL}")
                            print(f"{Fore.YELLOW}{EMOJI['INFO']} 和: chmod 644 {storage_path}{Style.RESET_ALL}")
                    
                    # Try to read the file to verify it's not corrupted
                    try:
                        with open(storage_path, 'r') as f:
                            content = f.read()
                            if not content.strip():
                                print(f"{Fore.YELLOW}{EMOJI['WARNING']} 存储文件为空: {storage_path}{Style.RESET_ALL}")
                                print(f"{Fore.YELLOW}{EMOJI['INFO']} 文件可能已损坏，请重新安装 Cursor{Style.RESET_ALL}")
                            else:
                                print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 存储文件有效且包含数据{Style.RESET_ALL}")
                    except Exception as e:
                        print(f"{Fore.RED}{EMOJI['ERROR']} 读取存储文件时出错: {str(e)}{Style.RESET_ALL}")
                        print(f"{Fore.YELLOW}{EMOJI['INFO']} 文件可能已损坏。请重新安装 Cursor{Style.RESET_ALL}")
                elif storage_path:
                    print(f"{Fore.YELLOW}{EMOJI['WARNING']} 未找到存储文件: {storage_path}{Style.RESET_ALL}")
                    print(f"{Fore.YELLOW}{EMOJI['INFO']} 请确保 Cursor 已安装并至少运行过一次{Style.RESET_ALL}")
                
            except (OSError, IOError) as e:
                print(f"{Fore.RED}{EMOJI['ERROR']} 检查 Linux 路径时出错: {str(e)}{Style.RESET_ALL}")
            
            # Define all paths using the found cursor directory
            default_config['LinuxPaths'] = {
                'storage_path': storage_path,
                'sqlite_path': os.path.abspath(os.path.join(cursor_dir, "User/globalStorage/state.vscdb")) if cursor_dir else "",
                'machine_id_path': os.path.join(cursor_dir, "machineid") if cursor_dir else "",
                'cursor_path': get_linux_cursor_path(),
                'updater_path': os.path.join(config_base, "cursor-updater"),
                'update_yml_path': os.path.join(cursor_dir, "resources/app-update.yml") if cursor_dir else "",
                'product_json_path': os.path.join(cursor_dir, "resources/app/product.json") if cursor_dir else ""
            }

        # Add tempmail_plus configuration
        default_config['TempMailPlus'] = {
            'enabled': 'false',
            'email': '',
            'epin': ''
        }

        # Read existing configuration and merge
        if os.path.exists(config_file):
            config.read(config_file, encoding='utf-8')
            config_modified = False
            
            for section, options in default_config.items():
                if not config.has_section(section):
                    config.add_section(section)
                    config_modified = True
                for option, value in options.items():
                    if not config.has_option(section, option):
                        config.set(section, option, str(value))
                        config_modified = True
                        safe_log(f"已添加配置选项: {section}.{option}")

            if config_modified:
                with open(config_file, 'w', encoding='utf-8') as f:
                    config.write(f)
                safe_log("配置已更新")
        else:
            for section, options in default_config.items():
                config.add_section(section)
                for option, value in options.items():
                    config.set(section, option, str(value))

            with open(config_file, 'w', encoding='utf-8') as f:
                config.write(f)
            safe_log(f"配置已创建: {config_file}")

        return config

    except Exception as e:
        safe_log(f"设置配置时出错: {str(e)}", 'error')
        return None
    
def print_config(config):
    """Log configuration in a readable format (for GUI app, no longer logs to file)"""
    if not config:
        safe_log("配置不可用", 'warning')
        return

    safe_log("配置信息:")
    for section in config.sections():
        safe_log(f"[{section}]")
        for key, value in config.items(section):
            # 对布尔值进行特殊处理
            if value.lower() in ('true', 'yes', 'on', '1'):
                value_display = "已启用"
            elif value.lower() in ('false', 'no', 'off', '0'):
                value_display = "已禁用"
            else:
                value_display = value

            safe_log(f"  {key} = {value_display}")

    config_dir = os.path.join(get_user_documents_path(), ".cursor-free-vip", "config.ini")
    safe_log(f"配置目录: {config_dir}")

def force_update_config():
    """
    Force update configuration file with latest defaults if update check is enabled.
    Args:
        translator: Translator instance
    Returns:
        ConfigParser instance or None if failed
    """
    try:
        config_dir = os.path.join(get_user_documents_path(), ".cursor-free-vip")
        config_file = os.path.join(config_dir, "config.ini")
        current_time = datetime.datetime.now()

        # If the config file exists, check if forced update is enabled
        if os.path.exists(config_file):
            # First, read the existing configuration
            existing_config = configparser.ConfigParser()
            existing_config.read(config_file, encoding='utf-8')
            # Check if "enabled_update_check" is True
            update_enabled = True  # Default to True if not set
            if existing_config.has_section('Utils') and existing_config.has_option('Utils', 'enabled_force_update'):
                update_enabled = existing_config.get('Utils', 'enabled_force_update').strip().lower() in ('true', 'yes', '1', 'on')

            if update_enabled:
                try:
                    # Create a backup
                    backup_file = f"{config_file}.bak.{current_time.strftime('%Y%m%d_%H%M%S')}"
                    shutil.copy2(config_file, backup_file)
                    print(f"\n{Fore.CYAN}{EMOJI['INFO']} 已创建备份: {backup_file}{Style.RESET_ALL}")
                    print(f"\n{Fore.CYAN}{EMOJI['INFO']} 配置文件强制更新已启用{Style.RESET_ALL}")
                    # Delete the original config file (forced update)
                    os.remove(config_file)
                    print(f"{Fore.CYAN}{EMOJI['INFO']} 配置文件已删除以进行强制更新{Style.RESET_ALL}")
                except Exception as e:
                    print(f"{Fore.RED}{EMOJI['ERROR']} 备份配置失败: {str(e)}{Style.RESET_ALL}")
            else:
                print(f"\n{Fore.CYAN}{EMOJI['INFO']} 配置文件强制更新已被配置禁用。保留现有配置文件。{Style.RESET_ALL}")

        # Generate a new (or updated) configuration if needed
        return setup_config()

    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 强制更新配置失败: {str(e)}{Style.RESET_ALL}")
        return None

def get_config():
    """Get existing config or create new one"""
    global _config_cache
    if _config_cache is None:
        _config_cache = setup_config()
    return _config_cache

def save_config(config):
    """Saves the provided config object to the config file."""
    try:
        # get documents path
        docs_path = get_user_documents_path()
        if not docs_path or not os.path.exists(docs_path):
            docs_path = os.path.abspath('.')

        config_dir = os.path.normpath(os.path.join(docs_path, ".cursor-free-vip"))
        config_file = os.path.normpath(os.path.join(config_dir, "config.ini"))
        
        with open(config_file, 'w', encoding='utf-8') as f:
            config.write(f)
        
        # Invalidate cache so it's re-read next time
        global _config_cache
        _config_cache = config
        
        return True
    except Exception as e:
        safe_log(f"保存配置时出错: {e}", 'error')
        return False