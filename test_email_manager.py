#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试邮箱管理模块
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from 邮箱管理 import EmailManager, email_manager
    print("✅ 邮箱管理模块导入成功")
    
    # 测试邮箱管理器
    print(f"📧 当前邮箱前缀: {email_manager.email_prefix}")
    print(f"🔑 当前邮箱密码: {'已设置' if email_manager.email_password else '未设置'}")
    
    # 测试生成随机邮箱
    random_email = email_manager.generate_random_email()
    if random_email:
        print(f"🎲 生成的随机邮箱: {random_email}")
    else:
        print("⚠️ 无法生成随机邮箱，请先设置邮箱前缀")
    
    # 测试监控邮箱
    monitoring_email = email_manager.get_monitoring_email()
    if monitoring_email:
        print(f"👁️ 监控邮箱: {monitoring_email}")
    else:
        print("⚠️ 无法获取监控邮箱，请先设置邮箱前缀")
    
    print("✅ 邮箱管理模块测试完成")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
except Exception as e:
    print(f"❌ 测试错误: {e}")
    import traceback
    traceback.print_exc()
