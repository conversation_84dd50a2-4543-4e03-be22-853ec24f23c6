import sys
import os
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import traceback
import threading
import multiprocessing
import platform
import random
import pywinauto
from pywinauto.application import Application

from 配置管理 import get_config, save_config
from 应用管理 import run_full_reset_flow
from 邮箱管理 import email_manager, show_email_client, run_auto_login_flow

# === 工具模块内容 ===
# Define emoji constants
EMOJI = {
    "INFO": "ℹ️",
    "ERROR": "❌",
    "SUCCESS": "✅",
    "WARNING": "⚠️",
    "LOGIN": "🔑",
    "EMAIL": "📧",
    "USER": "👤",
    "COPY": "📋",
    "RESET": "🔄",
    "START": "▶️",
    "STOP": "⏹️"
}

def get_user_documents_path():
    """Get user documents path"""
    if platform.system() == "Windows":
        try:
            import winreg
            # 打开注册表
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Shell Folders") as key:
                # 获取 "Personal" 键的值，这指向用户的文档目录
                documents_path, _ = winreg.QueryValueEx(key, "Personal")
                return documents_path
        except Exception as e:
            # fallback
            return os.path.expanduser("~\\Documents")
    else:
        return os.path.expanduser("~/Documents")

def get_current_chrome_url():
    """
    Gets the URL of the active tab in Google Chrome.
    Returns the URL as a string or None if not found.
    """
    try:
        app = Application(backend="uia").connect(title_re=".*- Google Chrome", class_name="Chrome_WidgetWin_1", timeout=10)
        dlg = app.top_window()
        wrapper = dlg.child_window(auto_id="view_1000", control_type="ToolBar")
        address_bar = wrapper.child_window(control_type="Edit")

        if address_bar.exists():
            url = address_bar.get_value()
            return url, dlg # Return both URL and the window handle
        else:
            # Error: Could not find the address bar (silent for GUI app)
            return None, None

    except pywinauto.findwindows.ElementNotFoundError:
        print("Error: Google Chrome window not found.")
        return None, None
    except Exception as e:
        print(f"An unknown error occurred: {e}")
        return None, None

def get_default_driver_path(browser_type='chrome'):
    """Get default driver path for Chrome."""
    return get_default_chrome_driver_path()

def get_default_chrome_driver_path():
    """Get default Chrome driver path"""
    if sys.platform == "win32":
        return os.path.join(os.path.dirname(os.path.abspath(__file__)), "drivers", "chromedriver.exe")
    elif sys.platform == "darwin":
        return os.path.join(os.path.dirname(os.path.abspath(__file__)), "drivers", "chromedriver")
    else:
        return "/usr/local/bin/chromedriver"

def get_default_browser_path(browser_type='chrome'):
    """Get default Chrome browser executable path"""
    # Platform-specific logic
    if sys.platform == "win32":
        exe_names = ["chrome.exe"]

        # 1. Try to find via Windows Registry App Paths
        try:
            import winreg
            for name in exe_names:
                key_path = rf"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\{name}"
                for root in [winreg.HKEY_LOCAL_MACHINE, winreg.HKEY_CURRENT_USER]:
                    try:
                        with winreg.OpenKey(root, key_path) as key:
                            path, _ = winreg.QueryValueEx(key, "")
                            if os.path.exists(path):
                                return path
                    except FileNotFoundError:
                        continue
        except (ImportError, OSError):
            pass # winreg might not be available or other OS error

        # 2. Try to find in PATH environment variable
        try:
            import shutil
            for name in exe_names:
                path_from_env = shutil.which(name)
                if path_from_env:
                    return path_from_env
        except ImportError:
            pass

        # 3. Fallback to hardcoded default paths
        return r"C:\Program Files\Google\Chrome\Application\chrome.exe"

    elif sys.platform == "darwin":
        # macOS paths
        return "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"

    else:  # Linux
        # For Linux, rely on shutil.which to find in PATH
        try:
            import shutil
            # Create a list of possible executable names
            names_to_try = ["chrome", "google-chrome", "chromium", "chromium-browser"]

            for name in names_to_try:
                path = shutil.which(name)
                if path:
                    return path
        except ImportError:
            pass # shutil might not be available

        # Fallback to common hardcoded paths for Linux
        return "/usr/bin/google-chrome"

def get_linux_cursor_path():
    """Get Linux Cursor path"""
    possible_paths = [
        "/opt/Cursor/resources/app",
        "/usr/share/cursor/resources/app",
        "/opt/cursor-bin/resources/app",
        "/usr/lib/cursor/resources/app",
        os.path.expanduser("~/.local/share/cursor/resources/app")
    ]

    # return the first path that exists
    return next((path for path in possible_paths if os.path.exists(path)), possible_paths[0])

def get_random_wait_time(config, timing_key):
    """Get random wait time based on configuration timing settings

    Args:
        config (dict): Configuration dictionary containing timing settings
        timing_key (str): Key to look up in the timing settings

    Returns:
        float: Random wait time in seconds
    """
    try:
        # Get timing value from config
        timing = config.get('Timing', {}).get(timing_key)
        if not timing:
            # Default to 0.5-1.5 seconds if timing not found
            return random.uniform(0.5, 1.5)

        # Check if timing is a range (e.g., "0.5-1.5" or "0.5,1.5")
        if isinstance(timing, str):
            if '-' in timing:
                min_time, max_time = map(float, timing.split('-'))
            elif ',' in timing:
                min_time, max_time = map(float, timing.split(','))
            else:
                # Single value, use it as both min and max
                min_time = max_time = float(timing)
        else:
            # If timing is a number, use it as both min and max
            min_time = max_time = float(timing)

        return random.uniform(min_time, max_time)

    except (ValueError, TypeError, AttributeError):
        # Return default value if any error occurs
        return random.uniform(0.5, 1.5)

# === 界面组件内容 ===
# 定义现代化的颜色主题
COLORS = {
    'primary': '#2563eb',      # 蓝色主色调
    'primary_light': '#3b82f6',
    'secondary': '#10b981',    # 绿色辅助色
    'accent': '#f59e0b',       # 橙色强调色
    'danger': '#ef4444',       # 红色警告色
    'background': '#f8fafc',   # 浅灰背景
    'surface': '#ffffff',      # 白色表面
    'text_primary': '#1f2937', # 深灰文字
    'text_secondary': '#6b7280', # 中灰文字
    'border': '#e5e7eb'        # 边框颜色
}

# 定义字体样式
FONTS = {
    'title': ('Microsoft YaHei UI', 12, 'bold'),
    'subtitle': ('Microsoft YaHei UI', 10, 'bold'),
    'body': ('Microsoft YaHei UI', 9),
    'small': ('Microsoft YaHei UI', 8),
    'mono': ('Consolas', 9),
    'mono_bold': ('Consolas', 10, 'bold')
}

def create_auto_login_frame(parent):
    """创建美化的一键操作框架"""
    frame = ttk.LabelFrame(parent, text=f"{EMOJI['LOGIN']} 一键操作工具", padding="15")

    # 添加描述文字
    desc_label = ttk.Label(frame, text="快速执行登录和环境重置操作",
                          font=FONTS['small'], foreground=COLORS['text_secondary'])
    desc_label.pack(pady=(0, 15))

    # 按钮容器
    button_frame = ttk.Frame(frame)
    button_frame.pack(fill=tk.X)

    # 一键登录按钮（左边）
    login_button = ttk.Button(button_frame, text=f"{EMOJI['LOGIN']} 一键登录")
    login_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5), ipady=6)

    # 一键重置环境按钮（右边）
    reset_button = ttk.Button(button_frame, text=f"{EMOJI['RESET']} 一键重置环境")
    reset_button.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(5, 0), ipady=6)

    return frame, login_button, reset_button

def create_unified_email_frame(parent):
    """创建统一的邮箱管理框架"""
    frame = ttk.LabelFrame(parent, text=f"{EMOJI['EMAIL']} 邮箱管理中心", padding="15")

    # 添加描述文字
    desc_label = ttk.Label(frame, text="统一管理邮箱凭据、随机邮箱生成和验证码监控",
                          font=FONTS['small'], foreground=COLORS['text_secondary'])
    desc_label.grid(row=0, column=0, columnspan=3, sticky="w", pady=(0, 15))

    # 邮箱凭据设置区域
    cred_frame = ttk.LabelFrame(frame, text="📋 凭据设置", padding="10")
    cred_frame.grid(row=1, column=0, columnspan=3, sticky="ew", pady=(0, 15))
    cred_frame.columnconfigure(1, weight=1)

    # 邮箱前缀
    ttk.Label(cred_frame, text="邮箱前缀:", font=FONTS['body']).grid(row=0, column=0, sticky="w", pady=(0, 10))
    prefix_entry = ttk.Entry(cred_frame, font=FONTS['mono'], width=25)
    prefix_entry.insert(0, email_manager.email_prefix)
    prefix_entry.grid(row=0, column=1, sticky="ew", padx=(10, 0), pady=(0, 10))

    # 显示完整邮箱格式
    format_label = ttk.Label(cred_frame, text="@2925.com",
                           font=FONTS['small'], foreground=COLORS['text_secondary'])
    format_label.grid(row=0, column=2, sticky="w", padx=(5, 0), pady=(0, 10))

    # 邮箱密码
    ttk.Label(cred_frame, text="邮箱密码:", font=FONTS['body']).grid(row=1, column=0, sticky="w", pady=(0, 10))
    password_entry = ttk.Entry(cred_frame, font=FONTS['mono'], width=25)
    password_entry.insert(0, email_manager.email_password)
    password_entry.grid(row=1, column=1, columnspan=2, sticky="ew", padx=(10, 0), pady=(0, 10))

    # 随机邮箱显示区域
    random_frame = ttk.LabelFrame(frame, text="🎲 随机邮箱", padding="10")
    random_frame.grid(row=2, column=0, columnspan=3, sticky="ew", pady=(0, 15))
    random_frame.columnconfigure(1, weight=1)

    # 状态指示器
    status_dot = ttk.Label(random_frame, text="●", foreground=COLORS['secondary'], font=FONTS['body'])
    status_dot.grid(row=0, column=0, sticky="w")

    info_label = ttk.Label(random_frame, text="自动生成随机邮箱 (每秒更新)",
                          font=FONTS['small'], foreground=COLORS['text_secondary'])
    info_label.grid(row=0, column=1, sticky="w", padx=(5, 0))

    # 邮箱显示
    ttk.Label(random_frame, text="邮箱:", font=FONTS['body']).grid(row=1, column=0, sticky="w", pady=(10, 0))
    email_entry = ttk.Entry(random_frame, state='readonly', font=FONTS['mono'], width=40)
    email_entry.grid(row=1, column=1, sticky="ew", padx=(10, 10), pady=(10, 0))

    email_copy_button = ttk.Button(random_frame, text=f"{EMOJI['COPY']} 复制")
    email_copy_button.grid(row=1, column=2, pady=(10, 0))

    # 按钮区域
    button_frame = ttk.Frame(frame)
    button_frame.grid(row=3, column=0, columnspan=3, sticky="ew", pady=(10, 0))

    # 保存凭据按钮（左边）
    save_button = ttk.Button(button_frame, text=f"{EMOJI['SUCCESS']} 保存凭据")
    save_button.pack(side=tk.LEFT, padx=(0, 10))

    # 邮箱监控器按钮（右边）
    email_client_button = ttk.Button(button_frame, text=f"{EMOJI['EMAIL']} 打开邮箱监控器")
    email_client_button.pack(side=tk.RIGHT)

    frame.columnconfigure(0, weight=1)
    return frame, prefix_entry, password_entry, email_entry, email_copy_button, save_button, email_client_button

def create_browser_settings_frame(parent, browser_path):
    """创建美化的浏览器设置框架"""
    frame = ttk.LabelFrame(parent, text="🌐 浏览器设置", padding="15")

    # 添加说明
    desc_label = ttk.Label(frame, text="配置Chrome浏览器路径用于自动化操作",
                          font=FONTS['small'], foreground=COLORS['text_secondary'])
    desc_label.grid(row=0, column=0, columnspan=3, sticky="w", pady=(0, 15))

    # 路径标签
    path_label = ttk.Label(frame, text="Chrome路径:", font=FONTS['body'])
    path_label.grid(row=1, column=0, sticky="w", pady=(0, 10))

    # 路径输入框
    path_entry = ttk.Entry(frame, font=FONTS['mono'], width=35)
    path_entry.insert(0, browser_path)
    path_entry.grid(row=1, column=1, sticky="ew", padx=(10, 10), pady=(0, 10))

    # 浏览按钮
    browse_button = ttk.Button(frame, text="📁 浏览...")
    browse_button.grid(row=1, column=2, pady=(0, 10))

    # 路径状态指示
    if browser_path and len(browser_path) > 0:
        status_text = "✅ 已配置"
        status_color = COLORS['secondary']
    else:
        status_text = "⚠️ 未配置"
        status_color = COLORS['accent']

    status_label = ttk.Label(frame, text=status_text,
                           font=FONTS['small'], foreground=status_color)
    status_label.grid(row=2, column=1, sticky="w", padx=(10, 0), pady=(0, 10))

    # 保存按钮
    save_button = ttk.Button(frame, text=f"{EMOJI['SUCCESS']} 保存路径")
    save_button.grid(row=2, column=2, sticky="e", pady=(0, 0))

    frame.columnconfigure(1, weight=1)
    return frame, path_entry, browse_button, save_button

# --- Global Exception Handler to catch errors when running with pythonw.exe ---
def setup_global_exception_handler():
    """Sets up a global exception handler to show error messages without logging to files."""
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return

        try:
            error_root = tk.Tk()
            error_root.withdraw()
            messagebox.showerror(
                "Fatal Error",
                f"A critical error occurred and the application must close.\n\n"
                f"Error: {exc_value}"
            )
            error_root.destroy()
        except Exception:
            pass

    sys.excepthook = handle_exception
# --- End Exception Handler ---

def create_gui():
    """
    Creates and runs the Tkinter GUI.
    """
    root = tk.Tk()
    root.title("🔑 续杯工具 - Cursor管理助手")

    # 设置窗口图标和样式
    try:
        root.iconbitmap(default='')  # 可以添加图标文件
    except:
        pass

    root.withdraw()
    
    try:
        config = get_config()
        if not config:
            messagebox.showerror("致命错误", "无法加载或创建配置文件。应用程序将退出。")
            root.destroy()
            return
            
        email_prefix = config.get('Email', 'prefix', fallback='').strip()
        email_password = config.get('Email', 'password', fallback='').strip()
        browser_path = config.get('Browser', 'chrome_path', fallback='')
        
        root.deiconify()
    except Exception as e:
        messagebox.showerror("初始化错误", f"加载配置时出错: {e}\n\n请检查 'error.log' 文件。")
        root.destroy()
        return

    # 窗口大小设置（不居中）
    root.geometry("900x700")
    root.resizable(True, True)
    root.minsize(800, 600)

    # 设置现代化的背景色
    root.configure(bg='#f8fafc')

    # 创建主容器
    mainframe = ttk.Frame(root, padding="20")
    mainframe.grid(row=0, column=0, sticky="nsew")
    root.columnconfigure(0, weight=1)
    root.rowconfigure(0, weight=1)

    # 创建标题区域
    title_frame = ttk.Frame(mainframe)
    title_frame.grid(row=0, column=0, columnspan=2, sticky="ew", pady=(0, 20))

    title_label = ttk.Label(title_frame, text="🔑 续杯工具",
                           font=("Microsoft YaHei UI", 16, "bold"))
    title_label.pack(side=tk.LEFT)

    subtitle_label = ttk.Label(title_frame, text="Cursor管理助手 - 让续杯更简单",
                              font=("Microsoft YaHei UI", 10), foreground="#6b7280")
    subtitle_label.pack(side=tk.LEFT, padx=(15, 0))

    # 设置主框架布局
    mainframe.columnconfigure(0, weight=1)
    mainframe.columnconfigure(1, weight=2)
    mainframe.rowconfigure(1, weight=1)

    # 左侧控制面板
    left_frame = ttk.Frame(mainframe)
    left_frame.grid(row=1, column=0, sticky="nsew", padx=(0, 15))
    left_frame.columnconfigure(0, weight=1)

    # 创建各个功能组件
    auto_login_frame, auto_login_button, reset_environment_button = create_auto_login_frame(left_frame)
    email_frame, prefix_entry, password_entry, email_entry, email_copy_button, credentials_save_button, open_email_client_button = create_unified_email_frame(left_frame)
    browser_settings_frame, browser_path_entry, browser_browse_button, browser_save_button = create_browser_settings_frame(left_frame, browser_path)

    # 右侧状态显示区域
    right_frame = ttk.LabelFrame(mainframe, text="📊 操作日志", padding="15")
    right_frame.grid(row=1, column=1, sticky="nsew")
    right_frame.columnconfigure(0, weight=1)
    right_frame.rowconfigure(0, weight=1)

    status_box = scrolledtext.ScrolledText(right_frame, wrap=tk.WORD, state='disabled',
                                          font=("Consolas", 9), height=20)

    # --- Layout ---
    # 左侧组件布局（按使用频率和逻辑顺序排列）
    auto_login_frame.grid(row=0, column=0, sticky="ew", pady=(0, 15))
    email_frame.grid(row=1, column=0, sticky="ew", pady=(0, 15))
    browser_settings_frame.grid(row=2, column=0, sticky="ew", pady=(0, 0))

    # 右侧状态框布局
    status_box.grid(row=0, column=0, sticky="nsew")
    
    # --- Logic ---
    def update_status(message):
        if not root.winfo_exists(): return

        # 检查是否是验证码复制消息
        if message.startswith("COPY_AND_SHOW:"):
            try:
                # 提取验证码
                code = message.split("COPY_AND_SHOW:", 1)[1].strip()

                # 自动复制到剪贴板
                root.clipboard_clear()
                root.clipboard_append(code)

                # 显示友好的消息（只在日志中显示，不弹窗）
                display_message = f"✅ 验证码已获取并自动复制到剪贴板: {code}"

                status_box.configure(state='normal')
                status_box.insert(tk.END, display_message + "\n")
                status_box.configure(state='disabled')
                status_box.see(tk.END)
                root.update_idletasks()

            except Exception as e:
                # 如果处理验证码失败，显示原始消息
                status_box.configure(state='normal')
                status_box.insert(tk.END, message + "\n")
                status_box.configure(state='disabled')
                status_box.see(tk.END)
                root.update_idletasks()
        else:
            # 普通消息的处理
            status_box.configure(state='normal')
            status_box.insert(tk.END, message + "\n")
            status_box.configure(state='disabled')
            status_box.see(tk.END)
            root.update_idletasks()
    
    def update_random_email():
        email_entry.config(state='normal')
        email_entry.delete(0, tk.END)

        new_email = email_manager.generate_random_email()
        if not new_email:
            update_status(f"{EMOJI['ERROR']} 邮箱前缀未设置，无法生成邮箱。请在GUI中设置。")
            new_email = ""
        email_entry.insert(0, new_email)

        email_entry.config(state='readonly')
        root.after(1000, update_random_email) # Update every 1s

    def clear_status_and_disable_buttons():
        reset_environment_button.configure(state='disabled')
        auto_login_button.configure(state='disabled')
        open_email_client_button.configure(state='disabled')
        status_box.configure(state='normal')
        status_box.delete('1.0', tk.END)
        status_box.configure(state='disabled')

    def re_enable_buttons():
        if not root.winfo_exists(): return
        reset_environment_button.configure(state='normal')
        auto_login_button.configure(state='normal')
        open_email_client_button.configure(state='normal')

    def copy_to_clipboard(text_to_copy, item_name):
        if not root.winfo_exists(): return
        root.clipboard_clear()
        root.clipboard_append(text_to_copy)
        update_status(f"'{item_name}' 已复制到剪贴板。")

    def handle_open_email_client_click():
        """打开独立的邮箱客户端窗口"""
        try:
            show_email_client(root)
            update_status(f"{EMOJI['SUCCESS']} 邮箱客户端已打开")
        except Exception as e:
            update_status(f"{EMOJI['ERROR']} 打开邮箱客户端失败: {e}")

    def handle_reset_environment_click():
        def task():
            root.after(0, clear_status_and_disable_buttons)
            try:
                run_full_reset_flow(lambda msg: root.after(0, update_status, msg))
            except Exception as e:
                root.after(0, lambda e=e: update_status(f"{EMOJI['ERROR']} 环境重置过程中发生未知错误: {e}"))
                root.after(0, lambda: update_status(traceback.format_exc()))
            finally:
                re_enable_buttons()
        
        threading.Thread(target=task, daemon=True).start()

    def handle_auto_login_click():
        def task():
            root.after(0, clear_status_and_disable_buttons)
            try:
                password = password_entry.get()
                # 固定的监控邮箱
                monitoring_email = email_manager.get_monitoring_email()
                # 界面上显示的随机邮箱用于登录
                login_email = email_entry.get()

                run_auto_login_flow(monitoring_email, login_email, password, lambda msg: root.after(0, update_status, msg))
            except Exception as e:
                root.after(0, lambda e=e: update_status(f"错误: 一键登录过程中发生未知错误: {e}"))
                root.after(0, lambda: update_status(traceback.format_exc()))
            finally:
                root.after(0, re_enable_buttons)

        threading.Thread(target=task, daemon=True).start()

    def handle_save_credentials_click():
        nonlocal email_prefix, email_password
        new_prefix = prefix_entry.get().strip()
        new_password = password_entry.get() # Don't strip password

        if email_manager.save_credentials(new_prefix, new_password):
            email_prefix = new_prefix
            email_password = new_password
            update_status(f"{EMOJI['SUCCESS']} 凭据已保存。")

            # Refresh the random email display
            email_entry.config(state='normal')
            email_entry.delete(0, tk.END)
            updated_email = email_manager.generate_random_email() or ""
            email_entry.insert(0, updated_email)
            email_entry.config(state='readonly')
        else:
            update_status(f"错误: 保存凭据失败。")

    def handle_browse_browser_path():
        from tkinter import filedialog
        # 在Windows上，只显示exe文件
        filetypes = (('Executable files', '*.exe'), ('All files', '*.*')) if sys.platform == "win32" else None
        filepath = filedialog.askopenfilename(title="选择Chrome浏览器可执行文件", filetypes=filetypes)
        if filepath:
            browser_path_entry.delete(0, tk.END)
            browser_path_entry.insert(0, filepath)
            update_status(f"浏览器路径已更新为: {filepath}")

    def handle_save_browser_path():
        nonlocal browser_path
        new_path = browser_path_entry.get().strip()
        if not new_path or not os.path.exists(new_path):
            update_status(f"错误: 路径无效或文件不存在: {new_path}")
            return
            
        config.set('Browser', 'chrome_path', new_path)

        if save_config(config):
            browser_path = new_path
            update_status(f"{EMOJI['SUCCESS']} 浏览器路径已保存。")
        else:
            update_status(f"错误: 保存浏览器路径失败。")

    email_copy_button.configure(command=lambda: copy_to_clipboard(email_entry.get(), "邮箱"))
    reset_environment_button.configure(command=handle_reset_environment_click)
    auto_login_button.configure(command=handle_auto_login_click)
    credentials_save_button.configure(command=handle_save_credentials_click)
    open_email_client_button.configure(command=handle_open_email_client_click)
    browser_browse_button.configure(command=handle_browse_browser_path)
    browser_save_button.configure(command=handle_save_browser_path)
    
    update_status("准备就绪。请点击按钮开始。")
    update_random_email()

    root.mainloop()

if __name__ == "__main__":
    # For multiprocessing to work correctly when packaged
    multiprocessing.freeze_support()

    # 确保在无终端环境下也能正常运行
    # 不再重定向输出到日志文件

    setup_global_exception_handler()
    create_gui()